"""
Enhanced Agent module for the Advanced AI Agent.
Comprehensive implementation with robust tool system, context management, and advanced features.
"""

import re
import json
import time
import traceback
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Generator, Callable
from dataclasses import dataclass
from enum import Enum

from models import ModelManager
from conversation import Conversation, Message, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager
)
# Advanced tools will be imported after logger initialization
HAS_ADVANCED_TOOLS = False
from tools.threading.thread_manager import ThreadManager
from tools.predictive.prefetcher import PredictivePrefetcher
from tools.predictive.context_analyzer import ContextAnalyzer
from tools.predictive.code_predictor import CodePredictor
from core.enhanced_agent import EnhancedAgent, AgentMode, TaskType
from core.intelligent_refactoring import IntelligentRefactoring
from core.iterative_execution_manager import IterativeExecutionManager

# Import all available core components
try:
    from core.adaptive_controller import Adaptive<PERSON><PERSON>roller
    from core.advanced_cache_system import AdvancedCacheSystem
    from core.advanced_code_understanding import AdvancedCodeUnderstanding
    from core.context_aware_completion import ContextAwareCompletion
    from core.dependency_analyzer import DependencyAnalyzer
    from core.enhanced_performance_monitor import EnhancedPerformanceMonitor
    from core.error_detector import ErrorDetector
    from core.execution_monitor import ExecutionMonitor
    from core.learning_system import LearningSystem
    from core.multi_language_processor import MultiLanguageProcessor
    from core.optimization_engine import OptimizationEngine
    from core.optimization_recommender import OptimizationRecommender
    from core.performance_analyzer import PerformanceAnalyzer
    from core.predictive_debugger import PredictiveDebugger
    from core.rag_enhanced_generator import RAGEnhancedGenerator
    from core.rag_enhanced_system import RAGEnhancedSystem
    from core.self_analyzer import SelfAnalyzer
    from core.self_analyzing_intelligence import SelfAnalyzingIntelligence
    from core.semantic_indexer import SemanticIndexer
    HAS_ADVANCED_CORE = True
except ImportError as e:
    HAS_ADVANCED_CORE = False
from tools.tool_manager import Tool, ToolManager
from core.ai_code_assistant import AICodeAssistant, AssistantRequest, AssistantResponse
from utils import get_logger

# Get the logger
logger = get_logger()

# Import additional advanced tools after logger initialization
try:
    from tools.ai_agent_enhancer import AIAgentEnhancer
    from tools.optimized_search_engine import OptimizedSearchEngine
    from tools.nlp.intent_recognizer import IntentRecognizer
    from tools.nlp.code_understanding import CodeUnderstanding
    from tools.nlp.nl_to_code import NLToCode
    HAS_ADVANCED_TOOLS = True
    logger.info("Advanced tools imported successfully")
except ImportError as e:
    logger.warning(f"Some advanced tools not available: {e}")
    HAS_ADVANCED_TOOLS = False

class ToolExecutionResult:
    """Result of tool execution with enhanced metadata."""

    def __init__(self, success: bool, result: str, tool_name: str,
                 execution_time: float = 0.0, error: Optional[str] = None):
        self.success = success
        self.result = result
        self.tool_name = tool_name
        self.execution_time = execution_time
        self.error = error
        self.timestamp = time.time()

class AgentContext:
    """Enhanced context management for the agent."""

    def __init__(self):
        self.conversation_history: List[Dict[str, Any]] = []
        self.tool_usage_history: List[Dict[str, Any]] = []
        self.current_task_context: Dict[str, Any] = {}
        self.user_preferences: Dict[str, Any] = {}
        self.session_metadata: Dict[str, Any] = {}

    def add_tool_usage(self, tool_name: str, parameters: Dict[str, Any],
                      result: ToolExecutionResult):
        """Add tool usage to history for context awareness."""
        self.tool_usage_history.append({
            'tool_name': tool_name,
            'parameters': parameters,
            'result': result.result,
            'success': result.success,
            'execution_time': result.execution_time,
            'timestamp': result.timestamp
        })

    def get_recent_tool_usage(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent tool usage for context."""
        return self.tool_usage_history[-limit:] if self.tool_usage_history else []

    def update_task_context(self, key: str, value: Any):
        """Update current task context."""
        self.current_task_context[key] = value

class EnhancedAgent:
    """Enhanced AI agent with advanced tool system and context management."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the enhanced agent.

        Args:
            model_manager: The model manager to use.
            conversation_manager: The conversation manager to use.
            workspace_dir: The workspace directory to use. If None, will use the current directory.
            system_prompt: The system prompt to use. If None, will use a default prompt.
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()

        # Initialize enhanced context management
        self.context = AgentContext()

        # Initialize tool manager with enhanced capabilities
        self.tool_manager = ToolManager()

        # Initialize advanced features
        self.iterative_mode = False
        self.performance_mode = False
        self.debug_mode = False

        # Tool execution statistics
        self.tool_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0
        }

        # Initialize tools with enhanced error handling
        self._has_rag = False
        self._initialize_enhanced_tools()

        # Initialize AI Code Assistant with enhanced capabilities
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Initialize advanced components
        self._initialize_advanced_components()

        # Set the enhanced system prompt
        if system_prompt is None:
            self.system_prompt = self._get_enhanced_system_prompt()
        else:
            self.system_prompt = system_prompt

        logger.info("Enhanced Agent initialized successfully with advanced features")

    def _get_enhanced_system_prompt(self) -> str:
        """Get the enhanced system prompt with comprehensive tool guidance."""
        # Get the list of available tools with enhanced descriptions
        tools = self.tool_manager.get_all_tools()
        tools_list = []
        tool_examples = []

        for tool in tools:
            # Add tool to the list with enhanced description
            tools_list.append(f"- **{tool.name}**: {tool.description}")

            # Add comprehensive example usage
            if tool.name == "shell":
                tool_examples.append(f"""
**{tool.name}** - Execute system commands:
```tool_code
{{
  "tool_name": "shell",
  "parameters": {{
    "command": "ls -la"
  }}
}}
```""")
            elif tool.name == "code_execute":
                tool_examples.append(f"""
**{tool.name}** - Execute code in various languages:
```tool_code
{{
  "tool_name": "code_execute",
  "parameters": {{
    "language": "python",
    "code": "print('Hello, World!')"
  }}
}}
```""")
            elif tool.name == "file_read":
                tool_examples.append(f"""
**{tool.name}** - Read file contents:
```tool_code
{{
  "tool_name": "file_read",
  "parameters": {{
    "path": "example.txt"
  }}
}}
```""")

        tools_list_str = "\n".join(tools_list)
        tool_examples_str = "\n".join(tool_examples[:6])  # Show key examples

        return f"""
You are an advanced AI coding agent with comprehensive capabilities for software development, analysis, and automation.

## CORE CAPABILITIES:
- Code generation, analysis, and optimization
- File system operations and project management
- Web research and information gathering
- System administration and automation
- Multi-language programming support
- Intelligent debugging and problem-solving

## AVAILABLE TOOLS:
{tools_list_str}

## TOOL USAGE FORMAT:
When you need to use a tool, use this exact JSON format:
```tool_code
{{
  "tool_name": "tool_name_here",
  "parameters": {{
    "param1": "value1",
    "param2": "value2"
  }}
}}
```

## TOOL EXAMPLES:
{tool_examples_str}

## BEST PRACTICES:
1. **Context Awareness**: Consider previous interactions and tool usage
2. **Error Handling**: Always validate inputs and handle errors gracefully
3. **Efficiency**: Choose the most appropriate tool for each task
4. **Clarity**: Explain your reasoning and approach clearly
5. **Safety**: Be cautious with system commands and file operations

## RESPONSE GUIDELINES:
- Provide clear explanations of your actions and reasoning
- Use tools strategically to accomplish user goals
- Maintain context across multiple interactions
- Ask for clarification when requirements are ambiguous
- Be helpful, accurate, and professional

Remember: You have access to advanced capabilities. Use these tools intelligently to provide comprehensive assistance.
"""

    @property
    def tools(self):
        """Get a dictionary of available tools with enhanced metadata."""
        return {tool.name: tool.function for tool in self.tool_manager.get_all_tools()}

    @property
    def tool_descriptions(self):
        """Get detailed tool descriptions for context awareness."""
        return {tool.name: tool.description for tool in self.tool_manager.get_all_tools()}

    def _initialize_enhanced_tools(self):
        """Initialize and register tools with enhanced error handling and capabilities."""
        logger.info("Initializing enhanced tool system...")

        try:
            # Initialize individual tool instances with enhanced error handling
            self.shell_tool = ShellTool(self.workspace_dir)
            self.file_tool = FileTool(self.workspace_dir)
            self.code_tool = CodeTool()
            self.web_tool = WebTool()
            self.codebase_tool = CodebaseTool(self.workspace_dir)
            self.vision_tool = VisionTool(self.workspace_dir)
            self.patch_tool = PatchTool(self.workspace_dir)
            self.browser_tool = BrowserTool()

            # Initialize search and information tools
            self.search_api = SearchAPI()
            self.web_scraper = WebScraperTool()
            self.info_synthesizer = InformationSynthesizer()
            self.web_info_manager = WebInfoManager()

            # Initialize RAG tool if dependencies are available
            self._initialize_rag_tool()

            # Register all tools with enhanced capabilities
            self._register_enhanced_tools()

            logger.info(f"Successfully initialized {len(self.tools)} tools")

        except Exception as e:
            logger.error(f"Error initializing tools: {e}")
            raise

    def _initialize_rag_tool(self):
        """Initialize RAG tool with proper dependency checking."""
        try:
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
                logger.info("RAG tool initialized successfully")
            else:
                self._has_rag = False
                logger.warning("RAG tool dependencies not available (faiss, sentence_transformers)")
        except Exception as e:
            self._has_rag = False
            logger.warning(f"Failed to initialize RAG tool: {e}")

    def _initialize_advanced_components(self):
        """Initialize all available advanced agent components."""
        try:
            # Initialize predictive components
            self.thread_manager = ThreadManager()
            self.context_analyzer = ContextAnalyzer()
            self.code_predictor = CodePredictor()

            # Initialize enhanced agent components
            self.enhanced_agent = EnhancedAgent()
            self.intelligent_refactoring = IntelligentRefactoring()
            self.iterative_execution_manager = IterativeExecutionManager()

            # Initialize all available core components if available
            if HAS_ADVANCED_CORE:
                try:
                    self.adaptive_controller = AdaptiveController()
                    self.advanced_cache_system = AdvancedCacheSystem()
                    self.advanced_code_understanding = AdvancedCodeUnderstanding()
                    self.context_aware_completion = ContextAwareCompletion()
                    self.dependency_analyzer = DependencyAnalyzer()
                    self.enhanced_performance_monitor = EnhancedPerformanceMonitor()
                    self.error_detector = ErrorDetector()
                    self.execution_monitor = ExecutionMonitor()
                    self.learning_system = LearningSystem()
                    self.multi_language_processor = MultiLanguageProcessor()
                    self.optimization_engine = OptimizationEngine()
                    self.optimization_recommender = OptimizationRecommender()
                    self.performance_analyzer = PerformanceAnalyzer()
                    self.predictive_debugger = PredictiveDebugger()
                    self.rag_enhanced_generator = RAGEnhancedGenerator()
                    self.rag_enhanced_system = RAGEnhancedSystem()
                    self.self_analyzer = SelfAnalyzer()
                    self.self_analyzing_intelligence = SelfAnalyzingIntelligence()
                    self.semantic_indexer = SemanticIndexer()

                    logger.info("All advanced core components initialized successfully")
                except Exception as e:
                    logger.warning(f"Some advanced core components failed to initialize: {e}")

            # Initialize predictive prefetcher with proper dependencies
            try:
                self.predictive_prefetcher = PredictivePrefetcher(
                    self.thread_manager,
                    self.context_analyzer,
                    self.code_predictor,
                    self.model_manager
                )
                logger.info("Predictive prefetcher initialized successfully")
            except Exception as e:
                logger.warning(f"Predictive prefetcher failed to initialize: {e}")

            logger.info("Advanced components initialization completed")
        except Exception as e:
            logger.warning(f"Some advanced components failed to initialize: {e}")

    def _register_enhanced_tools(self):
        """Register all tools with enhanced capabilities and error handling."""
        self._register_shell_tools()
        self._register_file_tools()
        self._register_code_tools()
        self._register_web_tools()
        self._register_codebase_tools()
        self._register_vision_tools()
        self._register_patch_tools()
        self._register_browser_tools()
        self._register_info_tools()
        self._register_ai_assistant_tool()
        if self._has_rag:
            self._register_rag_tool()

        # Register advanced Augment-like tools
        self._register_augment_like_tools()

        # Register all advanced core component tools
        self._register_advanced_core_tools()

    def _register_shell_tools(self):
        self.tool_manager.register_tool(Tool(
            name="shell",
            description="Execute shell commands on the system.",
            function=self._execute_shell,
            parameters={
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "The shell command to execute."}
                },
                "required": ["command"]
            }
        ))

    def _execute_shell(self, args) -> str:
        """Execute a shell command with enhanced error handling and logging.

        Args:
            args: The command to execute (string or dict with 'command' key).

        Returns:
            The command output or error message.
        """
        start_time = time.time()

        # Enhanced parameter handling
        if isinstance(args, dict):
            command = args.get("command", "")
            working_dir = args.get("working_dir", str(self.workspace_dir))
        else:
            command = str(args).strip()
            working_dir = str(self.workspace_dir)

        if not command:
            return "Error: No command specified."

        try:
            logger.info(f"Executing shell command: {command}")
            stdout, stderr, return_code = self.shell_tool.execute(command)

            execution_time = time.time() - start_time
            self._update_tool_stats('shell', execution_time, return_code == 0)

            if return_code == 0:
                result = f"Command executed successfully:\n\n{stdout}" if stdout else "Command executed successfully (no output)"
                logger.info(f"Shell command completed successfully in {execution_time:.2f}s")
                return result
            else:
                error_msg = f"Command failed (exit code {return_code}):\n\n{stderr}"
                logger.warning(f"Shell command failed with exit code {return_code}")
                return error_msg

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_tool_stats('shell', execution_time, False)
            error_msg = f"Error executing command: {e}"
            logger.error(f"Shell command execution error: {e}")
            return error_msg

    def _update_tool_stats(self, tool_name: str, execution_time: float, success: bool):
        """Update tool execution statistics."""
        self.tool_stats['total_executions'] += 1
        if success:
            self.tool_stats['successful_executions'] += 1
        else:
            self.tool_stats['failed_executions'] += 1

        # Update average execution time
        total_time = self.tool_stats['average_execution_time'] * (self.tool_stats['total_executions'] - 1)
        self.tool_stats['average_execution_time'] = (total_time + execution_time) / self.tool_stats['total_executions']

    def _register_file_tools(self):
        self.tool_manager.register_tool(Tool(
            name="file_read",
            description="Read the content of a file.",
            function=self._file_read,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to read."}
                },
                "required": ["path"]
            }
        ))

    def _register_code_tools(self):
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _register_web_tools(self):
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _execute_web_search(self, args) -> str:
        """Execute a web search operation.

        Args:
            args: The search query (string or dict with 'query' key)

        Returns:
            Search results in JSON format
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            query = args.get("query", "")
        else:
            query = str(args)

        if not query:
            return "Error: No search query specified."

        try:
            results = self.web_tool.search(query)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error performing web search: {str(e)}"

    def _execute_web_fetch(self, args) -> str:
        """Fetch content from a URL.

        Args:
            args: The URL to fetch (string or dict with 'url' key)

        Returns:
            Fetched content with metadata
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            url = args.get("url", "")
        else:
            url = str(args)

        if not url:
            return "Error: No URL specified."

        try:
            content, metadata = self.web_tool.fetch_url(url)
            return (
                f"URL: {url}\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars
            )
        except Exception as e:
            return f"Error fetching URL: {str(e)}"

    def _register_codebase_tools(self):
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_find_code_files",
            description="Find all code files in the codebase.",
            function=self._execute_codebase_find_code_files,
            parameters={"type": "object", "properties": {}, "required": []}
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_analyze",
            description="Analyze a specific code file for structure and potential issues.",
            function=self._execute_codebase_analyze,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the code file to analyze."}
                },
                "required": ["path"]
            }
        ))

    def _execute_codebase_find_files(self, args = "*") -> str:
        """Find files in the codebase matching a pattern.

        Args:
            args: The pattern to search for (string or dict with 'pattern' key). Defaults to "*".

        Returns:
            List of matching files with their paths.
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            pattern = args.get("pattern", "*")
        else:
            pattern = str(args) if args else "*"

        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self) -> str:
        """Find all code files in the codebase.

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, args) -> str:
        """Search for a pattern within code files.

        Args:
            args: Search parameters (dict with 'pattern' and optional 'file_pattern', or string pattern).

        Returns:
            Search results with matching lines and context.
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            pattern = args.get("pattern", "")
            file_pattern = args.get("file_pattern", "*")
        else:
            pattern = str(args)
            file_pattern = "*"

        if not pattern:
            return "Error: No search pattern specified."

        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, args) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            args: The path to the code file to analyze (string or dict with 'path' key).

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            path = args.get("path", "")
        else:
            path = str(args)

        if not path.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(path)
            if analysis:
                formatted_analysis = {
                    "file": path,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {path}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {path}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _register_vision_tools(self):
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="vision_load_image",
            description="Load an image from a given path.",
            function=self._execute_vision_load_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the image file."}
                },
                "required": ["path"]
            }
        ))

    def _execute_vision_take_screenshot(self, path: str = None) -> str:
        """Take a screenshot of the current screen.

        Args:
            path: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, path: str) -> str:
        """Load an image from a given path.

        Args:
            path: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not path.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(path)
            return (
                f"Image loaded successfully:\n"
                f"Path: {path}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"

    def _register_patch_tools(self):
        """Register patch-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _register_browser_tools(self):
        """Register browser-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_code(self, args) -> str:
        """Execute code in various programming languages with enhanced capabilities.

        Args:
            args: The language and code to execute (dict with 'language' and 'code' keys, or string).

        Returns:
            The execution result or error message.
        """
        start_time = time.time()

        # Enhanced parameter handling
        if isinstance(args, dict):
            language = args.get("language", "").lower()
            code = args.get("code", "")
            timeout = args.get("timeout", 30)  # Default 30 second timeout
            save_output = args.get("save_output", False)
        else:
            # Parse the arguments (language and code) from string
            args_parts = str(args).strip().split(maxsplit=1)
            if len(args_parts) < 2:
                return "Error: Both language and code must be specified."
            language = args_parts[0].lower()
            code = args_parts[1]
            timeout = 30
            save_output = False

        if not language or not code:
            return "Error: Both language and code must be specified."

        # Validate supported languages
        supported_languages = ['python', 'javascript', 'java', 'cpp', 'c', 'go', 'rust', 'ruby', 'php']
        if language not in supported_languages:
            return f"Error: Unsupported language '{language}'. Supported: {', '.join(supported_languages)}"

        try:
            logger.info(f"Executing {language} code (length: {len(code)} chars)")

            # Execute the code with timeout
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            execution_time = time.time() - start_time
            self._update_tool_stats('code_execute', execution_time, return_code == 0)

            if return_code == 0:
                result = f"Code executed successfully:\n\n{stdout}" if stdout else "Code executed successfully (no output)"
                logger.info(f"Code execution completed successfully in {execution_time:.2f}s")

                # Save output if requested
                if save_output and stdout:
                    self.context.update_task_context('last_code_output', stdout)

                return result
            else:
                error_msg = f"Code execution failed (exit code {return_code}):\n\n{stderr}"
                logger.warning(f"Code execution failed with exit code {return_code}")
                return error_msg

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_tool_stats('code_execute', execution_time, False)
            error_msg = f"Error executing code: {e}"
            logger.error(f"Code execution error: {e}")
            return error_msg

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read_file(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse file path and content
            parts = args_parts[1].split(maxsplit=1)
            if len(parts) < 2:
                return "Error: No file content specified."

            file_path = parts[0]
            content = parts[1]

            try:
                self.file_tool.write_file(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete_file(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files
            path = args_parts[1] if len(args_parts) > 1 else "."

            try:
                files = self.file_tool.list_files(path)
                return f"Files in {path}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}"

    def _file_read(self, args) -> str:
        """Read the content of a file."""
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            path = args.get("path", "")
        else:
            path = str(args)

        if not path:
            return "Error: No file path specified."

        try:
            return self.file_tool.read_file(path)
        except Exception as e:
            return f"Error reading file: {e}"

    def _file_write(self, path: str, content: str) -> str:
        """Write content to a file. Overwrites if the file exists."""
        try:
            self.file_tool.write_file(path, content)
            return f"File written successfully: {path}"
        except Exception as e:
            return f"Error writing file: {e}"

    def _file_append(self, path: str, content: str) -> str:
        """Append content to an existing file."""
        try:
            self.file_tool.append_file(path, content)
            return f"Content appended successfully to: {path}"
        except Exception as e:
            return f"Error appending to file: {e}"

    def _file_delete(self, path: str) -> str:
        """Delete a file."""
        try:
            self.file_tool.delete_file(path)
            return f"File deleted successfully: {path}"
        except Exception as e:
            return f"Error deleting file: {e}"

    def _file_list(self, path: str = ".") -> str:
        """List files and directories in a given path."""
        try:
            files = self.file_tool.list_files(path)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error listing files: {e}"

    def _file_search(self, pattern: str) -> str:
        """Search for files matching a pattern."""
        try:
            files = self.file_tool.search_files(pattern)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error searching for files: {e}"

    def _file_grep(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within files."""
        try:
            results = self.file_tool.grep_files(pattern, file_pattern)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error grepping files: {e}"








    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))

    def _execute_search_web(self, args: str) -> str:
        """Perform a web search using multiple fallback methods.

        Args:
            args: The search query and optional number of results.

        Returns:
            The search results in JSON format.
        """
        # Parse the arguments (query and optional num_results)
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search query specified."

        query = args_parts[0]
        num_results = 5  # Default

        if len(args_parts) > 1:
            try:
                num_results = int(args_parts[1])
            except ValueError:
                pass  # Use default if invalid number

        try:
            results = self.search_api.search(query, num_results=num_results)
            if results:
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            else:
                return f"No results found for query: {query}"
        except Exception as e:
            return f"Error performing web search: {e}"

    def _register_scrape_tools(self):
        """Register web scraping tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="scrape_web",
            description="Scrape content from a website with advanced fallback mechanisms.",
            function=self._execute_scrape_web,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to scrape."}
                },
                "required": ["url"]
            }
        ))

    def _execute_scrape_web(self, args: str) -> str:
        """Scrape content from a website using multiple fallback methods.

        Args:
            args: The URL to scrape.

        Returns:
            The scraped content in a structured format.
        """
        if not args.strip():
            return "Error: No URL specified for scraping."

        try:
            # Scrape the URL with fallback methods
            scraped_data = self.web_scraper.scrape(args)

            if scraped_data:
                return f"Scraped content from {args}:\n\n{json.dumps(scraped_data, indent=2)}"
            else:
                return f"No content could be scraped from {args}"
        except Exception as e:
            return f"Error scraping website: {e}"

    def _register_info_tools(self):
        """Register information processing tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="info_retrieve",
            description="Retrieve and synthesize information about a topic.",
            function=self._execute_info_retrieve,
            parameters={
                "type": "object",
                "properties": {
                    "topic": {"type": "string", "description": "The topic to retrieve information about."}
                },
                "required": ["topic"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_extract",
            description="Extract key information from a given text.",
            function=self._execute_info_extract,
            parameters={
                "type": "object",
                "properties": {
                    "text": {"type": "string", "description": "The text from which to extract information."}
                },
                "required": ["text"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_format",
            description="Format information in a structured manner (e.g., markdown, JSON).",
            function=self._execute_info_format,
            parameters={
                "type": "object",
                "properties": {
                    "information": {"type": "string", "description": "The information to format."},
                    "format_type": {"type": "string", "description": "The desired format (e.g., 'markdown', 'json')."}
                },
                "required": ["information", "format_type"]
            }
        ))

    def _execute_info_retrieve(self, args: str) -> str:
        """Retrieve and synthesize information about a topic.

        Args:
            args: The topic to retrieve information about.

        Returns:
            The synthesized information about the topic.
        """
        if not args.strip():
            return "Error: No topic specified for information retrieval."

        try:
            result = self.info_synthesizer.retrieve(args)
            if result:
                return f"Information about '{args}':\n\n{result}"
            else:
                return f"No information found about topic: {args}"
        except Exception as e:
            return f"Error retrieving information: {e}"

    def _execute_info_extract(self, args: str) -> str:
        """Extract key information from a given text.

        Args:
            args: The text to extract information from.

        Returns:
            The extracted key information in a structured format.
        """
        if not args.strip():
            return "Error: No text provided for information extraction."

        try:
            extracted_info = self.info_synthesizer.extract(args)
            return f"Extracted information:\n\n{json.dumps(extracted_info, indent=2)}"
        except Exception as e:
            return f"Error extracting information: {e}"

    def _execute_info_format(self, args: str) -> str:
        """Format information in a specified structure.

        Args:
            args: The information and format type separated by a delimiter.

        Returns:
            The formatted information.
        """
        # Parse the arguments (information and format_type)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both information and format type must be specified."

        information = args_parts[0]
        format_type = args_parts[1].lower()

        try:
            formatted_info = self.info_synthesizer.format(information, format_type)
            return f"Formatted information ({format_type}):\n\n{formatted_info}"
        except Exception as e:
            return f"Error formatting information: {e}"

    def _register_ai_assistant_tool(self):
        self.tool_manager.register_tool(Tool(
            name="ai_assistant",
            description="Advanced AI code analysis, generation, and optimization.",
            function=self._execute_ai_assistant,
            parameters={
                "type": "object",
                "properties": {
                    "request_type": {"type": "string", "description": "Type of request (e.g., 'analyze', 'generate', 'optimize')."},
                    "content": {"type": "string", "description": "Content for the request (e.g., code, problem description)."},
                    "language": {"type": "string", "description": "Programming language if applicable."}
                },
                "required": ["request_type", "content"]
            }
        ))

    def _execute_ai_assistant(self, args) -> str:
        """Execute an AI assistant operation.

        Args:
            args: The request parameters (dict with 'request_type', 'content', and optional 'language', or string).

        Returns:
            The result of the AI assistant operation.
        """
        # Handle both string and dictionary parameters
        if isinstance(args, dict):
            request_type = args.get("request_type", "")
            content = args.get("content", "")
            language = args.get("language", "python")
        else:
            # Parse the arguments (request_type and content) from string
            args_parts = str(args).strip().split(maxsplit=1)
            if len(args_parts) < 2:
                return "Error: Both request type and content must be specified."
            request_type = args_parts[0]
            content = args_parts[1]
            language = "python"  # Default to python if not specified

        if not request_type or not content:
            return "Error: Both request type and content must be specified."

        try:
            # Create an assistant request
            request = AssistantRequest(
                request_type=request_type,
                content=content,
                language=language
            )

            # Process the request
            response = self.ai_code_assistant.process_request(request)

            if response.success:
                return f"AI Assistant response ({request_type}):\n\n{response.content}"
            else:
                return f"AI Assistant error: {response.error_message}"
        except Exception as e:
            return f"Error processing AI assistant request: {e}"

    def _register_rag_tool(self):
        self.tool_manager.register_tool(Tool(
            name="rag",
            description="Retrieval-augmented generation for answering questions based on a knowledge base.",
            function=self._execute_rag,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The query for RAG."}
                },
                "required": ["query"]
            }
        ))

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation to answer questions based on knowledge base.

        Args:
            args: The query to process with RAG.

        Returns:
            The response from RAG system with relevant information.
        """
        if not args.strip():
            return "Error: No query specified for RAG operation."

        if not self._has_rag:
            return "Error: RAG functionality is not available. Required dependencies (faiss, sentence-transformers) not found."

        try:
            # Process the RAG query
            result = self.rag_tool.query(args)

            if result:
                return f"RAG response for '{args}':\n\n{result}"
            else:
                return f"No relevant information found for query: {args}"
        except Exception as e:
            return f"Error processing RAG query: {e}"

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt.

        Returns:
            The default system prompt.
        """
        tools_list_str = ""
        for tool in self.tool_manager.get_all_tools():
            tools_list_str += f"- {tool.name}: {tool.description}\n"

        tool_examples = self.tool_manager.generate_tool_prompt_examples()

        return f"""
You are an advanced AI coding agent that can help with various tasks.
You have access to the following tools:
{tools_list_str}

When you need to use a tool, use the following JSON format:
```tool_code
{{
  "tool_name": "tool_name_here",
  "parameters": {{
    "param1": "value1",
    "param2": "value2"
  }}
}}
```

Here are examples of how to use each tool:
{tool_examples}

Always provide clear explanations of what you're doing and why.
If you're not sure about something, ask for clarification.
Be helpful, accurate, and concise.
"""

    def _prepare_conversation_history(self, conversation: Conversation) -> List[Dict[str, Any]]:
        """Prepare conversation history for the model.

        Args:
            conversation: The conversation to prepare history from.

        Returns:
            List of message dictionaries for the model.
        """
        # Convert conversation messages to the format expected by the model
        history = []

        # Add system prompt as the first message if not already in conversation
        if not any(msg.role == "system" for msg in conversation.messages):
            history.append({
                "role": "system",
                "content": self.system_prompt
            })

        # Add all messages from the conversation
        for message in conversation.messages:
            # Skip tool messages for now as they're handled separately
            if message.role not in ["tool", "tool_result"]:
                history.append({
                    "role": message.role,
                    "content": message.content
                })

        return history

    def process_message(self, message: str, conversation: Optional[Conversation] = None) -> str:
        """Process a message from the user.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.

        Returns:
            The response from the agent.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response = self.model_manager.generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        )

        # Process the response for tool calls
        processed_response = self._process_response(response, conversation)

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

        # Analyze the tool result and provide a status update
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = list(re.finditer(tool_pattern, response, re.DOTALL))
        if tool_matches:
            tool_result = conversation.messages[-1].content
            match = tool_matches[0]
            tool_name = match.group(1)
            tool_args = match.group(2).strip()
            status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
            return f"{processed_response}\n\n{status_update}"

        return processed_response

    def execute_iteratively(self, message: str) -> Generator[str, None, None]:
        """Execute a message iteratively with step-by-step analysis."""
        try:
            yield "🔍 **Analyzing request...**"

            # Analyze the request
            analysis = self._analyze_request(message)
            yield f"📋 **Analysis:** {analysis['summary']}"

            if analysis['requires_tools']:
                yield f"🛠️ **Tools needed:** {', '.join(analysis['suggested_tools'])}"

                # Execute tools step by step
                for i, tool_step in enumerate(analysis['execution_steps'], 1):
                    yield f"\n**Step {i}:** {tool_step['description']}"

                    try:
                        # Execute the tool
                        tool_result = self.tools[tool_step['tool']](tool_step['parameters'])
                        yield f"✅ **Result:** {tool_result[:200]}..." if len(tool_result) > 200 else f"✅ **Result:** {tool_result}"

                        # Update context
                        self.context.update_task_context(f'step_{i}_result', tool_result)

                    except Exception as e:
                        yield f"❌ **Error in step {i}:** {e}"

            yield "\n🎯 **Task completed successfully!**"

        except Exception as e:
            yield f"❌ **Iterative execution failed:** {e}"

    def _analyze_request(self, message: str) -> Dict[str, Any]:
        """Analyze a user request to determine execution strategy."""
        # Simple analysis - in a real implementation, this would use NLP
        analysis = {
            'summary': 'Request analysis completed',
            'requires_tools': False,
            'suggested_tools': [],
            'execution_steps': []
        }

        # Check for common patterns
        if any(keyword in message.lower() for keyword in ['list', 'ls', 'directory', 'files']):
            analysis['requires_tools'] = True
            analysis['suggested_tools'] = ['shell']
            analysis['execution_steps'] = [{
                'tool': 'shell',
                'parameters': {'command': 'ls -la'},
                'description': 'List directory contents'
            }]
        elif any(keyword in message.lower() for keyword in ['execute', 'run', 'code']):
            analysis['requires_tools'] = True
            analysis['suggested_tools'] = ['code_execute']
        elif any(keyword in message.lower() for keyword in ['read', 'file', 'content']):
            analysis['requires_tools'] = True
            analysis['suggested_tools'] = ['file_read']

        return analysis

    def execute_with_error_recovery(self, message: str) -> str:
        """Execute with enhanced error recovery mechanisms."""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                return self.process_message(message)
            except Exception as e:
                retry_count += 1
                logger.warning(f"Execution attempt {retry_count} failed: {e}")

                if retry_count < max_retries:
                    # Try with simplified approach
                    try:
                        return f"Error occurred, but here's a simplified response: {str(e)}"
                    except:
                        continue
                else:
                    return f"Failed after {max_retries} attempts. Last error: {e}"

    def get_tool_statistics(self) -> Dict[str, Any]:
        """Get comprehensive tool usage statistics."""
        return {
            'total_executions': self.tool_stats['total_executions'],
            'successful_executions': self.tool_stats['successful_executions'],
            'failed_executions': self.tool_stats['failed_executions'],
            'success_rate': (self.tool_stats['successful_executions'] / max(1, self.tool_stats['total_executions'])) * 100,
            'average_execution_time': self.tool_stats['average_execution_time'],
            'recent_tool_usage': self.context.get_recent_tool_usage()
        }

    def analyze_project_structure(self, project_path: Optional[str] = None) -> Dict[str, Any]:
        """Analyze project structure and provide comprehensive insights."""
        if not project_path:
            project_path = str(self.workspace_dir)

        try:
            analysis = {
                'project_type': 'Unknown',
                'languages': [],
                'frameworks': [],
                'build_tools': [],
                'test_frameworks': [],
                'deployment_configs': [],
                'structure': {
                    'total_files': 0,
                    'source_files': [],
                    'test_files': [],
                    'config_files': []
                },
                'dependencies': {},
                'issues': [],
                'recommendations': []
            }

            # Use codebase tool to analyze structure
            files_result = self.codebase_tool.find_files("*")
            if files_result:
                analysis['structure']['total_files'] = len(files_result)

                # Categorize files
                for file_path in files_result:
                    if any(ext in file_path for ext in ['.py', '.js', '.java', '.cpp', '.c', '.go', '.rs']):
                        analysis['structure']['source_files'].append(file_path)

                        # Detect language
                        if file_path.endswith('.py'):
                            analysis['languages'].append('Python')
                        elif file_path.endswith('.js'):
                            analysis['languages'].append('JavaScript')
                        elif file_path.endswith('.java'):
                            analysis['languages'].append('Java')

                    elif 'test' in file_path.lower():
                        analysis['structure']['test_files'].append(file_path)
                    elif any(config in file_path for config in ['config', 'package.json', 'requirements.txt', 'Cargo.toml']):
                        analysis['structure']['config_files'].append(file_path)

                        # Detect frameworks and tools
                        if 'package.json' in file_path:
                            analysis['build_tools'].append('npm')
                        elif 'requirements.txt' in file_path:
                            analysis['build_tools'].append('pip')
                        elif 'Cargo.toml' in file_path:
                            analysis['build_tools'].append('cargo')

            # Remove duplicates
            analysis['languages'] = list(set(analysis['languages']))
            analysis['build_tools'] = list(set(analysis['build_tools']))

            # Add recommendations
            if not analysis['structure']['test_files']:
                analysis['issues'].append('No test files found')
                analysis['recommendations'].append('Consider adding unit tests')

            if analysis['structure']['total_files'] > 100 and not analysis['build_tools']:
                analysis['recommendations'].append('Consider using a build tool for better project management')

            return analysis

        except Exception as e:
            logger.error(f"Project analysis failed: {e}")
            return {'error': str(e)}

    def run_comprehensive_tests(self, test_type: str = "all") -> Dict[str, Any]:
        """Run comprehensive tests on the agent system."""
        test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': {},
            'recommendations': [],
            'performance_metrics': {}
        }

        try:
            start_time = time.time()

            if test_type in ["all", "tools"]:
                # Test all tools
                tool_tests = self._test_all_tools()
                test_results['test_details']['tools'] = tool_tests
                test_results['total_tests'] += tool_tests['total']
                test_results['passed_tests'] += tool_tests['passed']
                test_results['failed_tests'] += tool_tests['failed']

            if test_type in ["all", "integration"]:
                # Test integration
                integration_tests = self._test_integration()
                test_results['test_details']['integration'] = integration_tests
                test_results['total_tests'] += integration_tests['total']
                test_results['passed_tests'] += integration_tests['passed']
                test_results['failed_tests'] += integration_tests['failed']

            # Performance metrics
            execution_time = time.time() - start_time
            test_results['performance_metrics'] = {
                'response_time': execution_time,
                'memory_usage': 0.0  # Would need psutil for real memory usage
            }

            # Add recommendations based on results
            success_rate = (test_results['passed_tests'] / max(1, test_results['total_tests'])) * 100
            if success_rate < 80:
                test_results['recommendations'].append('Some tools are failing - check configurations')
            if execution_time > 10:
                test_results['recommendations'].append('Test execution is slow - consider optimization')

            return test_results

        except Exception as e:
            logger.error(f"Comprehensive testing failed: {e}")
            return {'error': str(e)}

    def _test_all_tools(self) -> Dict[str, Any]:
        """Test all available tools."""
        results = {'total': 0, 'passed': 0, 'failed': 0, 'details': {}}

        for tool_name in self.tools.keys():
            results['total'] += 1
            try:
                # Simple test for each tool
                if tool_name == 'shell':
                    test_result = self.tools[tool_name]({'command': 'echo test'})
                elif tool_name == 'code_execute':
                    test_result = self.tools[tool_name]({'language': 'python', 'code': 'print("test")'})
                else:
                    # Skip complex tools for basic testing
                    results['details'][tool_name] = {'status': 'skipped', 'reason': 'Complex tool - manual testing required'}
                    continue

                if 'Error' not in test_result:
                    results['passed'] += 1
                    results['details'][tool_name] = {'status': 'passed'}
                else:
                    results['failed'] += 1
                    results['details'][tool_name] = {'status': 'failed', 'error': test_result}

            except Exception as e:
                results['failed'] += 1
                results['details'][tool_name] = {'status': 'failed', 'error': str(e)}

        return results

    def _test_integration(self) -> Dict[str, Any]:
        """Test integration between components."""
        results = {'total': 1, 'passed': 0, 'failed': 0, 'details': {}}

        try:
            # Test basic integration
            test_message = "test integration"
            response = self.process_message(test_message)

            if response and len(response) > 0:
                results['passed'] += 1
                results['details']['basic_integration'] = {'status': 'passed'}
            else:
                results['failed'] += 1
                results['details']['basic_integration'] = {'status': 'failed', 'error': 'Empty response'}

        except Exception as e:
            results['failed'] += 1
            results['details']['basic_integration'] = {'status': 'failed', 'error': str(e)}

        return results

    def validate_system_health(self) -> Dict[str, Any]:
        """Validate overall system health."""
        health_check = {
            'overall_status': 'healthy',
            'timestamp': time.time(),
            'components': {},
            'warnings': [],
            'errors': [],
            'recommendations': []
        }

        try:
            # Check tool manager
            if self.tool_manager and len(self.tools) > 0:
                health_check['components']['tool_manager'] = 'healthy'
            else:
                health_check['components']['tool_manager'] = 'error'
                health_check['errors'].append('Tool manager not properly initialized')

            # Check model manager
            if self.model_manager:
                health_check['components']['model_manager'] = 'healthy'
            else:
                health_check['components']['model_manager'] = 'error'
                health_check['errors'].append('Model manager not available')

            # Check conversation manager
            if self.conversation_manager:
                health_check['components']['conversation_manager'] = 'healthy'
            else:
                health_check['components']['conversation_manager'] = 'error'
                health_check['errors'].append('Conversation manager not available')

            # Check workspace
            if self.workspace_dir and self.workspace_dir.exists():
                health_check['components']['workspace'] = 'healthy'
            else:
                health_check['components']['workspace'] = 'partial'
                health_check['warnings'].append('Workspace directory issues')

            # Determine overall status
            if health_check['errors']:
                health_check['overall_status'] = 'unhealthy'
            elif health_check['warnings']:
                health_check['overall_status'] = 'degraded'

            # Add recommendations
            if health_check['errors']:
                health_check['recommendations'].append('Fix critical errors before proceeding')
            if len(self.tools) < 5:
                health_check['recommendations'].append('Consider enabling more tools for better functionality')

            return health_check

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'overall_status': 'error',
                'timestamp': time.time(),
                'errors': [f'Health check failed: {e}']
            }

    def _register_augment_like_tools(self):
        """Register advanced tools similar to Augment Code capabilities."""

        # Codebase retrieval tool (like Augment's context engine)
        self.tool_manager.register_tool(Tool(
            name="codebase_retrieval",
            description="Retrieve relevant code context from the entire codebase using advanced search.",
            function=self._execute_codebase_retrieval,
            parameters={
                "type": "object",
                "properties": {
                    "information_request": {"type": "string", "description": "Description of the information you need from the codebase."}
                },
                "required": ["information_request"]
            }
        ))

        # Intelligent code generation tool
        self.tool_manager.register_tool(Tool(
            name="intelligent_code_gen",
            description="Generate intelligent code with context awareness and best practices.",
            function=self._execute_intelligent_code_gen,
            parameters={
                "type": "object",
                "properties": {
                    "description": {"type": "string", "description": "Description of the code to generate."},
                    "language": {"type": "string", "description": "Programming language."},
                    "context": {"type": "string", "description": "Additional context or requirements."}
                },
                "required": ["description", "language"]
            }
        ))

        # Advanced file operations
        self.tool_manager.register_tool(Tool(
            name="advanced_file_ops",
            description="Advanced file operations including creation, modification, and analysis.",
            function=self._execute_advanced_file_ops,
            parameters={
                "type": "object",
                "properties": {
                    "operation": {"type": "string", "description": "Operation type: create, modify, analyze, or search."},
                    "path": {"type": "string", "description": "File path."},
                    "content": {"type": "string", "description": "Content for create/modify operations."},
                    "search_pattern": {"type": "string", "description": "Pattern for search operations."}
                },
                "required": ["operation"]
            }
        ))

        # Project management tool
        self.tool_manager.register_tool(Tool(
            name="project_management",
            description="Advanced project management including dependency analysis and structure optimization.",
            function=self._execute_project_management,
            parameters={
                "type": "object",
                "properties": {
                    "action": {"type": "string", "description": "Action: analyze, optimize, or restructure."},
                    "target": {"type": "string", "description": "Target directory or file."},
                    "options": {"type": "object", "description": "Additional options for the action."}
                },
                "required": ["action"]
            }
        ))

        # Intelligent debugging tool
        self.tool_manager.register_tool(Tool(
            name="intelligent_debug",
            description="Intelligent debugging with error analysis and solution suggestions.",
            function=self._execute_intelligent_debug,
            parameters={
                "type": "object",
                "properties": {
                    "error_info": {"type": "string", "description": "Error message or code with issues."},
                    "context": {"type": "string", "description": "Additional context about the error."},
                    "language": {"type": "string", "description": "Programming language."}
                },
                "required": ["error_info"]
            }
        ))

    def _execute_codebase_retrieval(self, args) -> str:
        """Execute codebase retrieval similar to Augment's context engine."""
        if isinstance(args, dict):
            information_request = args.get("information_request", "")
        else:
            information_request = str(args)

        if not information_request:
            return "Error: No information request specified."

        try:
            # Use multiple search strategies
            results = []

            # 1. Search for relevant files
            file_results = self.codebase_tool.find_files("*")
            relevant_files = []

            # Simple relevance scoring based on keywords
            keywords = information_request.lower().split()
            for file_path in file_results[:20]:  # Limit to first 20 files
                score = 0
                file_lower = file_path.lower()
                for keyword in keywords:
                    if keyword in file_lower:
                        score += 1
                if score > 0:
                    relevant_files.append((file_path, score))

            # Sort by relevance
            relevant_files.sort(key=lambda x: x[1], reverse=True)

            # 2. Search within code
            for keyword in keywords[:3]:  # Limit to top 3 keywords
                try:
                    search_results = self.codebase_tool.search(keyword, "*.py")
                    if search_results:
                        results.append(f"Code search results for '{keyword}':")
                        for result in search_results[:3]:  # Top 3 results
                            results.append(f"  File: {result['file']}")
                            for match in result['matches'][:2]:  # Top 2 matches per file
                                results.append(f"    Line {match['line']}: {match['content'][:100]}...")
                except:
                    continue

            # 3. Analyze relevant files
            for file_path, score in relevant_files[:5]:  # Top 5 relevant files
                try:
                    analysis = self.codebase_tool.analyze(file_path)
                    if analysis:
                        results.append(f"\nAnalysis of {file_path}:")
                        results.append(f"  Language: {analysis.get('language', 'Unknown')}")
                        results.append(f"  Structure: {len(analysis.get('structure', []))} components")
                except:
                    continue

            if results:
                return f"Codebase retrieval results for: {information_request}\n\n" + "\n".join(results)
            else:
                return f"No relevant information found for: {information_request}"

        except Exception as e:
            return f"Error in codebase retrieval: {e}"

    def _execute_intelligent_code_gen(self, args) -> str:
        """Generate intelligent code with context awareness."""
        if isinstance(args, dict):
            description = args.get("description", "")
            language = args.get("language", "python")
            context = args.get("context", "")
        else:
            return "Error: Invalid parameters for code generation."

        if not description:
            return "Error: No code description provided."

        try:
            # Use AI assistant for intelligent code generation
            request = AssistantRequest(
                request_type="code_generation",
                content=f"Generate {language} code for: {description}. Context: {context}",
                language=language
            )

            response = self.ai_code_assistant.process_request(request)

            if response.success:
                # Add context-aware enhancements
                enhanced_code = self._enhance_generated_code(response.content, language, context)
                return f"Generated {language} code:\n\n```{language}\n{enhanced_code}\n```"
            else:
                return f"Code generation failed: {response.error_message}"

        except Exception as e:
            return f"Error in intelligent code generation: {e}"

    def _enhance_generated_code(self, code: str, language: str, context: str) -> str:
        """Enhance generated code with best practices and context awareness."""
        enhanced_code = code

        # Add common enhancements based on language
        if language.lower() == "python":
            if "def " in code and '"""' not in code:
                # Add docstrings to functions
                lines = code.split('\n')
                enhanced_lines = []
                for i, line in enumerate(lines):
                    enhanced_lines.append(line)
                    if line.strip().startswith('def ') and line.endswith(':'):
                        enhanced_lines.append('    """Function docstring."""')
                enhanced_code = '\n'.join(enhanced_lines)

        elif language.lower() == "javascript":
            if "function " in code and "/**" not in code:
                # Add JSDoc comments
                enhanced_code = "/**\n * Generated function\n */\n" + code

        return enhanced_code

    def _execute_advanced_file_ops(self, args) -> str:
        """Execute advanced file operations."""
        if isinstance(args, dict):
            operation = args.get("operation", "")
            path = args.get("path", "")
            content = args.get("content", "")
            search_pattern = args.get("search_pattern", "")
        else:
            return "Error: Invalid parameters for advanced file operations."

        if not operation:
            return "Error: No operation specified."

        try:
            if operation == "create":
                if not path or not content:
                    return "Error: Path and content required for create operation."

                # Create file with enhanced content
                file_path = Path(self.workspace_dir) / path
                file_path.parent.mkdir(parents=True, exist_ok=True)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                return f"File created successfully: {path}"

            elif operation == "modify":
                if not path:
                    return "Error: Path required for modify operation."

                file_path = Path(self.workspace_dir) / path
                if not file_path.exists():
                    return f"Error: File does not exist: {path}"

                # Read current content
                with open(file_path, 'r', encoding='utf-8') as f:
                    current_content = f.read()

                # Apply modifications (simple append for now)
                modified_content = current_content + "\n" + content

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)

                return f"File modified successfully: {path}"

            elif operation == "analyze":
                if not path:
                    return "Error: Path required for analyze operation."

                return self.codebase_tool.analyze(path)

            elif operation == "search":
                if not search_pattern:
                    return "Error: Search pattern required for search operation."

                results = self.codebase_tool.search(search_pattern, path or "*")
                return json.dumps(results, indent=2)

            else:
                return f"Error: Unknown operation: {operation}"

        except Exception as e:
            return f"Error in advanced file operations: {e}"

    def _execute_project_management(self, args) -> str:
        """Execute project management operations."""
        if isinstance(args, dict):
            action = args.get("action", "")
            target = args.get("target", str(self.workspace_dir))
            options = args.get("options", {})
        else:
            return "Error: Invalid parameters for project management."

        if not action:
            return "Error: No action specified."

        try:
            if action == "analyze":
                return json.dumps(self.analyze_project_structure(target), indent=2)

            elif action == "optimize":
                # Perform basic optimization analysis
                analysis = self.analyze_project_structure(target)
                optimizations = []

                if not analysis.get('structure', {}).get('test_files'):
                    optimizations.append("Add unit tests to improve code quality")

                if len(analysis.get('languages', [])) > 3:
                    optimizations.append("Consider consolidating languages for better maintainability")

                if analysis.get('structure', {}).get('total_files', 0) > 100:
                    optimizations.append("Consider modularizing the project structure")

                return f"Optimization suggestions:\n" + "\n".join(f"- {opt}" for opt in optimizations)

            elif action == "restructure":
                return "Project restructuring would require specific requirements. Please provide detailed restructuring goals."

            else:
                return f"Error: Unknown action: {action}"

        except Exception as e:
            return f"Error in project management: {e}"

    def _execute_intelligent_debug(self, args) -> str:
        """Execute intelligent debugging with error analysis."""
        if isinstance(args, dict):
            error_info = args.get("error_info", "")
            context = args.get("context", "")
            language = args.get("language", "python")
        else:
            error_info = str(args)
            context = ""
            language = "python"

        if not error_info:
            return "Error: No error information provided."

        try:
            # Analyze the error
            debug_analysis = {
                'error_type': 'Unknown',
                'possible_causes': [],
                'solutions': [],
                'code_suggestions': []
            }

            error_lower = error_info.lower()

            # Common error patterns
            if 'nameerror' in error_lower or 'not defined' in error_lower:
                debug_analysis['error_type'] = 'NameError'
                debug_analysis['possible_causes'] = [
                    'Variable or function not defined',
                    'Typo in variable name',
                    'Import statement missing'
                ]
                debug_analysis['solutions'] = [
                    'Check variable spelling',
                    'Ensure variable is defined before use',
                    'Add necessary import statements'
                ]

            elif 'syntaxerror' in error_lower:
                debug_analysis['error_type'] = 'SyntaxError'
                debug_analysis['possible_causes'] = [
                    'Missing parentheses or brackets',
                    'Incorrect indentation',
                    'Invalid syntax'
                ]
                debug_analysis['solutions'] = [
                    'Check for matching parentheses/brackets',
                    'Verify proper indentation',
                    'Review syntax for the language'
                ]

            elif 'indexerror' in error_lower:
                debug_analysis['error_type'] = 'IndexError'
                debug_analysis['possible_causes'] = [
                    'Accessing index beyond list/array bounds',
                    'Empty list/array access'
                ]
                debug_analysis['solutions'] = [
                    'Check list/array length before accessing',
                    'Use try-except for safe access',
                    'Validate index values'
                ]

            # Generate code suggestions based on error type
            if debug_analysis['error_type'] == 'IndexError' and language == 'python':
                debug_analysis['code_suggestions'] = [
                    'if len(my_list) > index:\n    value = my_list[index]',
                    'try:\n    value = my_list[index]\nexcept IndexError:\n    print("Index out of range")'
                ]

            result = f"Intelligent Debug Analysis:\n\n"
            result += f"Error Type: {debug_analysis['error_type']}\n\n"

            if debug_analysis['possible_causes']:
                result += "Possible Causes:\n"
                for cause in debug_analysis['possible_causes']:
                    result += f"- {cause}\n"
                result += "\n"

            if debug_analysis['solutions']:
                result += "Suggested Solutions:\n"
                for solution in debug_analysis['solutions']:
                    result += f"- {solution}\n"
                result += "\n"

            if debug_analysis['code_suggestions']:
                result += f"Code Suggestions ({language}):\n"
                for suggestion in debug_analysis['code_suggestions']:
                    result += f"```{language}\n{suggestion}\n```\n"

            return result

        except Exception as e:
            return f"Error in intelligent debugging: {e}"

    def _register_advanced_core_tools(self):
        """Register tools for all advanced core components."""
        if not HAS_ADVANCED_CORE:
            return

        try:
            # Performance analysis tool
            self.tool_manager.register_tool(Tool(
                name="performance_analysis",
                description="Analyze system and code performance with detailed metrics.",
                function=self._execute_performance_analysis,
                parameters={
                    "type": "object",
                    "properties": {
                        "target": {"type": "string", "description": "Target to analyze (code, system, or project)."},
                        "metrics": {"type": "array", "description": "Specific metrics to analyze."}
                    },
                    "required": ["target"]
                }
            ))

            # Optimization recommendations tool
            self.tool_manager.register_tool(Tool(
                name="optimization_recommendations",
                description="Get intelligent optimization recommendations for code and system.",
                function=self._execute_optimization_recommendations,
                parameters={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Code to optimize."},
                        "language": {"type": "string", "description": "Programming language."},
                        "focus": {"type": "string", "description": "Optimization focus (performance, memory, readability)."}
                    },
                    "required": ["code"]
                }
            ))

            # Dependency analysis tool
            self.tool_manager.register_tool(Tool(
                name="dependency_analysis",
                description="Analyze project dependencies and suggest improvements.",
                function=self._execute_dependency_analysis,
                parameters={
                    "type": "object",
                    "properties": {
                        "project_path": {"type": "string", "description": "Path to project for analysis."},
                        "analysis_type": {"type": "string", "description": "Type of analysis (security, updates, conflicts)."}
                    }
                }
            ))

            # Predictive debugging tool
            self.tool_manager.register_tool(Tool(
                name="predictive_debug",
                description="Advanced predictive debugging with AI-powered error prediction.",
                function=self._execute_predictive_debug,
                parameters={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Code to analyze for potential issues."},
                        "language": {"type": "string", "description": "Programming language."},
                        "context": {"type": "string", "description": "Additional context about the code."}
                    },
                    "required": ["code"]
                }
            ))

            # Self-analysis tool
            self.tool_manager.register_tool(Tool(
                name="self_analysis",
                description="Perform self-analysis of the AI agent's performance and capabilities.",
                function=self._execute_self_analysis,
                parameters={
                    "type": "object",
                    "properties": {
                        "analysis_type": {"type": "string", "description": "Type of self-analysis (performance, capabilities, health)."}
                    }
                }
            ))

            logger.info("Advanced core tools registered successfully")

        except Exception as e:
            logger.warning(f"Failed to register some advanced core tools: {e}")

    def _execute_performance_analysis(self, args) -> str:
        """Execute performance analysis using advanced components."""
        if isinstance(args, dict):
            target = args.get("target", "system")
            metrics = args.get("metrics", ["response_time", "memory", "cpu"])
        else:
            target = str(args)
            metrics = ["response_time", "memory", "cpu"]

        try:
            if hasattr(self, 'performance_analyzer'):
                analysis = self.performance_analyzer.analyze(target, metrics)
                return f"Performance Analysis Results:\n\n{json.dumps(analysis, indent=2)}"
            else:
                # Fallback to basic analysis
                return f"Basic performance analysis for {target}:\n- Tool statistics: {self.get_tool_statistics()}"

        except Exception as e:
            return f"Error in performance analysis: {e}"

    def _execute_optimization_recommendations(self, args) -> str:
        """Execute optimization recommendations."""
        if isinstance(args, dict):
            code = args.get("code", "")
            language = args.get("language", "python")
            focus = args.get("focus", "performance")
        else:
            return "Error: Invalid parameters for optimization recommendations."

        if not code:
            return "Error: No code provided for optimization."

        try:
            if hasattr(self, 'optimization_recommender'):
                recommendations = self.optimization_recommender.analyze_and_recommend(code, language, focus)
                return f"Optimization Recommendations:\n\n{json.dumps(recommendations, indent=2)}"
            else:
                # Fallback to basic recommendations
                basic_recommendations = []
                if "for " in code and "range(" in code:
                    basic_recommendations.append("Consider using list comprehensions for better performance")
                if "print(" in code:
                    basic_recommendations.append("Consider using logging instead of print statements")

                return f"Basic Optimization Recommendations:\n" + "\n".join(f"- {rec}" for rec in basic_recommendations)

        except Exception as e:
            return f"Error in optimization recommendations: {e}"

    def _execute_dependency_analysis(self, args) -> str:
        """Execute dependency analysis."""
        if isinstance(args, dict):
            project_path = args.get("project_path", str(self.workspace_dir))
            analysis_type = args.get("analysis_type", "general")
        else:
            project_path = str(self.workspace_dir)
            analysis_type = "general"

        try:
            if hasattr(self, 'dependency_analyzer'):
                analysis = self.dependency_analyzer.analyze_project(project_path, analysis_type)
                return f"Dependency Analysis Results:\n\n{json.dumps(analysis, indent=2)}"
            else:
                # Fallback to basic dependency analysis
                config_files = []
                try:
                    files = self.codebase_tool.find_files("*")
                    for file_path in files:
                        if any(config in file_path for config in ['package.json', 'requirements.txt', 'Cargo.toml', 'pom.xml']):
                            config_files.append(file_path)
                except:
                    pass

                return f"Basic Dependency Analysis:\nFound configuration files: {config_files}"

        except Exception as e:
            return f"Error in dependency analysis: {e}"

    def _execute_predictive_debug(self, args) -> str:
        """Execute predictive debugging."""
        if isinstance(args, dict):
            code = args.get("code", "")
            language = args.get("language", "python")
            context = args.get("context", "")
        else:
            code = str(args)
            language = "python"
            context = ""

        if not code:
            return "Error: No code provided for predictive debugging."

        try:
            if hasattr(self, 'predictive_debugger'):
                predictions = self.predictive_debugger.predict_issues(code, language, context)
                return f"Predictive Debug Results:\n\n{json.dumps(predictions, indent=2)}"
            else:
                # Fallback to basic predictive analysis
                potential_issues = []

                if language.lower() == "python":
                    if "import " not in code and "from " not in code and any(lib in code for lib in ['os', 'sys', 'json']):
                        potential_issues.append("Missing import statements detected")
                    if "def " in code and "return" not in code:
                        potential_issues.append("Function without return statement")
                    if "try:" in code and "except:" in code and "Exception" not in code:
                        potential_issues.append("Bare except clause - consider specific exception handling")

                return f"Basic Predictive Debug Analysis:\nPotential Issues:\n" + "\n".join(f"- {issue}" for issue in potential_issues)

        except Exception as e:
            return f"Error in predictive debugging: {e}"

    def _execute_self_analysis(self, args) -> str:
        """Execute self-analysis of the agent."""
        if isinstance(args, dict):
            analysis_type = args.get("analysis_type", "general")
        else:
            analysis_type = str(args) if args else "general"

        try:
            if hasattr(self, 'self_analyzer'):
                analysis = self.self_analyzer.analyze(analysis_type)
                return f"Self-Analysis Results:\n\n{json.dumps(analysis, indent=2)}"
            else:
                # Fallback to comprehensive self-analysis
                analysis = {
                    'agent_status': 'operational',
                    'tool_count': len(self.tools),
                    'tool_statistics': self.get_tool_statistics(),
                    'system_health': self.validate_system_health(),
                    'capabilities': {
                        'code_execution': 'available',
                        'file_operations': 'available',
                        'web_search': 'available',
                        'codebase_analysis': 'available',
                        'advanced_features': 'partial' if HAS_ADVANCED_CORE else 'basic'
                    },
                    'recommendations': []
                }

                # Add recommendations based on analysis
                if analysis['tool_statistics']['success_rate'] < 80:
                    analysis['recommendations'].append('Tool success rate is low - check configurations')
                if not HAS_ADVANCED_CORE:
                    analysis['recommendations'].append('Consider enabling advanced core components for enhanced capabilities')

                return f"Comprehensive Self-Analysis:\n\n{json.dumps(analysis, indent=2)}"

        except Exception as e:
            return f"Error in self-analysis: {e}"

    def stream_process_message(
        self,
        message: str,
        conversation: Optional[Conversation] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> Generator[str, None, None]:
        """Process a message from the user and stream the response.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.
            callback: A callback function to call with each chunk of the response.

        Yields:
            Chunks of the response.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response_chunks = []
        for chunk in self.model_manager.stream_generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        ):
            response_chunks.append(chunk)
            if callback:
                callback(chunk)
            yield chunk

        # Process the response for tool calls
        response = "".join(response_chunks)
        processed_response = self._process_response(response, conversation)

        # If the response was processed (tool calls), yield the processed response
        if processed_response != response:
            if callback:
                callback(processed_response)
            yield processed_response

        # Check for tool calls and analyze the result
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = list(re.finditer(tool_pattern, response, re.DOTALL))
        if tool_matches:
            try:
                tool_result = conversation.messages[-1].content
                match = tool_matches[0]
                tool_name = match.group(1)
                tool_args = match.group(2).strip()
                status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
                yield f"\n\n{status_update}"
            except (IndexError, AttributeError):
                pass

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

    def _process_response(self, response: str, conversation: Conversation) -> str:
        """Process a response for tool calls.

        Args:
            response: The response to process.
            conversation: The conversation to add tool messages to.

        Returns:
            The processed response.
        """
        # Check for tool_code blocks with JSON format
        tool_pattern = r"```tool_code\n(.*?)```"
        tool_matches = list(re.finditer(tool_pattern, response, re.DOTALL))

        # Process all tool calls
        processed_response = response
        for match in tool_matches:
            tool_json_str = match.group(1).strip()

            try:
                # Parse the JSON
                tool_call = json.loads(tool_json_str)
                tool_name = tool_call.get("tool_name")
                parameters = tool_call.get("parameters", {})

                if not tool_name:
                    error_msg = "Error: No tool_name specified in tool call"
                    processed_response = processed_response.replace(
                        match.group(0),
                        f"```tool_code\n{tool_json_str}\n```\n\n{error_msg}"
                    )
                    continue

                # Check if the tool exists
                if tool_name in self.tools:
                    try:
                        start_time = time.time()
                        logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")

                        # Execute the tool with enhanced parameter handling
                        if len(parameters) == 1 and list(parameters.keys())[0] in ["command", "query", "path", "language", "code"]:
                            # For tools that expect a single string parameter
                            tool_result = self.tools[tool_name](list(parameters.values())[0])
                        else:
                            # For tools that expect multiple parameters or structured data
                            tool_result = self.tools[tool_name](parameters)

                        execution_time = time.time() - start_time

                        # Create tool execution result for context tracking
                        result_obj = ToolExecutionResult(
                            success=True,
                            result=tool_result,
                            tool_name=tool_name,
                            execution_time=execution_time
                        )

                        # Add to context for future reference
                        self.context.add_tool_usage(tool_name, parameters, result_obj)

                        # Add the tool call and result to the conversation
                        conversation.add_message("tool", f"```tool_code\n{tool_json_str}\n```")
                        conversation.add_message("tool_result", tool_result)

                        # Replace the tool call with the result in the response
                        processed_response = processed_response.replace(
                            match.group(0),
                            f"```tool_code\n{tool_json_str}\n```\n\n**Tool executed successfully. Result:**\n{tool_result}"
                        )

                        logger.info(f"Tool {tool_name} executed successfully in {execution_time:.2f}s")

                    except Exception as tool_error:
                        execution_time = time.time() - start_time
                        error_msg = f"Error executing tool {tool_name}: {tool_error}"

                        # Create failed tool execution result
                        result_obj = ToolExecutionResult(
                            success=False,
                            result="",
                            tool_name=tool_name,
                            execution_time=execution_time,
                            error=str(tool_error)
                        )

                        # Add to context for future reference
                        self.context.add_tool_usage(tool_name, parameters, result_obj)

                        processed_response = processed_response.replace(
                            match.group(0),
                            f"```tool_code\n{tool_json_str}\n```\n\n{error_msg}"
                        )

                        logger.error(f"Tool {tool_name} execution failed: {tool_error}")
                else:
                    error_msg = f"Error: Unknown tool '{tool_name}'. Available tools: {', '.join(self.tools.keys())}"
                    processed_response = processed_response.replace(
                        match.group(0),
                        f"```tool_code\n{tool_json_str}\n```\n\n{error_msg}"
                    )

            except json.JSONDecodeError as e:
                error_msg = f"Error: Invalid JSON in tool call: {e}"
                processed_response = processed_response.replace(
                    match.group(0),
                    f"```tool_code\n{tool_json_str}\n```\n\n{error_msg}"
                )
            except Exception as e:
                error_msg = f"Error executing tool: {e}"
                processed_response = processed_response.replace(
                    match.group(0),
                    f"```tool_code\n{tool_json_str}\n```\n\n{error_msg}"
                )

        return processed_response

    def _analyze_tool_result(self, tool_name: str, tool_args: str, tool_result: str) -> str:
        """Analyze the results of a tool call and provide a clear status update.

        Args:
            tool_name: The name of the tool that was called.
            tool_args: The arguments that were passed to the tool.
            tool_result: The result of the tool call.

        Returns:
            A clear status update for the user.
        """
        # Analyze the tool result and provide a clear status update
        if "Error" in tool_result:
            return f"Tool {tool_name} failed with the following error: {tool_result}"
        else:
            return f"Tool {tool_name} executed successfully. Result: {tool_result}"

    def _execute_web(self, args: str) -> str:
        """Execute a web operation.

        Args:
            args: The arguments for the web operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No web operation specified."

        operation = args_parts[0]

        if operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                results = self.web_tool.search(query)
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching the web: {e}"

        elif operation == "fetch":
            # Fetch a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                content, metadata = self.web_tool.fetch_url(url)
                return f"Content from {url}:\n\nTitle: {metadata.get('title', 'N/A')}\n\n{content[:2000]}..."
            except Exception as e:
                return f"Error fetching URL: {e}"

        else:
            return f"Error: Unknown web operation: {operation}"

    def _execute_codebase(self, args: str) -> str:
        """Execute a codebase operation.

        Args:
            args: The arguments for the codebase operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No codebase operation specified."

        operation = args_parts[0]

        if operation == "find":
            # Find files
            pattern = args_parts[1] if len(args_parts) > 1 else "*"

            try:
                files = self.codebase_tool.find_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding files: {e}"

        elif operation == "find_code":
            # Find code files
            try:
                files = self.codebase_tool.find_code_files()
                return f"Code files found:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding code files: {e}"

        elif operation == "search":
            # Search for a pattern in code files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse the pattern and file pattern
            search_parts = args_parts[1].split(maxsplit=1)
            if len(search_parts) < 2:
                pattern = search_parts[0]
                file_pattern = "*"
            else:
                pattern = search_parts[0]
                file_pattern = search_parts[1]

            try:
                results = self.codebase_tool.search_code(pattern, file_pattern)
                return f"Search results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching code: {e}"

        elif operation == "analyze":
            # Analyze a code file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                results = self.codebase_tool.analyze_file(file_path)
                return f"Analysis results for {file_path}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error analyzing file: {e}"

        else:
            return f"Error: Unknown codebase operation: {operation}"

    def _execute_vision(self, args: str) -> str:
        """Execute a vision operation.

        Args:
            args: The arguments for the vision operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No vision operation specified."

        operation = args_parts[0]

        if operation == "take_screenshot":
            # Take a screenshot
            path = args_parts[1] if len(args_parts) > 1 else None

            try:
                screenshot_path = self.vision_tool.take_screenshot(path)
                return f"Screenshot taken and saved to: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "load_image":
            # Load an image
            if len(args_parts) < 2:
                return "Error: No image path specified."

            image_path = args_parts[1]

            try:
                image = self.vision_tool.load_image(image_path)
                return f"Image loaded: {image_path} (Size: {image.width}x{image.height})"
            except Exception as e:
                return f"Error loading image: {e}"

        else:
            return f"Error: Unknown vision operation: {operation}"


    def _execute_patch(self, args: str) -> str:
        """Execute a patch operation.

        Args:
            args: The arguments for the patch operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No patch operation specified."

        operation = args_parts[0]

        if operation == "apply":
            # Apply a patch
            if len(args_parts) < 2:
                return "Error: No patch arguments specified."

            # Parse the file path, original code, and updated code
            try:
                patch_parts = args_parts[1].split(maxsplit=2)
                if len(patch_parts) < 3:
                    return "Error: Insufficient patch arguments. Need file_path, original_code, and updated_code."

                file_path = patch_parts[0]
                original_code = patch_parts[1]
                updated_code = patch_parts[2]

                # Apply the patch
                result = self.patch_tool.apply_patch(file_path, original_code, updated_code)
                return f"Patch applied successfully to {file_path}"
            except Exception as e:
                return f"Error applying patch: {e}"

        else:
            return f"Error: Unknown patch operation: {operation}"
