"""
CLI interface for the Advanced AI Agent.
"""

import os
import sys
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

import click
from prompt_toolkit import PromptSession
from prompt_toolkit.history import FileHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.styles import Style
from rich.console import Console
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.panel import Panel

from agent import EnhancedAgent
from models import ModelManager
from conversation import ConversationManager
from config import load_config, save_config, get_config_dir
from utils import print_colored, print_error, print_success, print_info, print_warning, setup_logger

# Create a console for rich output
console = Console()

# Define the prompt style
style = Style.from_dict({
    "prompt": "bold #00aa00",
    "command": "bold #ffaa00",
})

# Define the command completer
command_completer = WordCompleter([
    "/help", "/exit", "/quit", "/history", "/list", "/load", "/new", "/delete",
    "/save", "/export", "/clear", "/models", "/config", "/debug", "/version",
    "/color", "/logging", "/iterative", "/performance", "/project", "/test", "/health", "!"
])

def display_welcome_message() -> None:
    """Display the welcome message."""
    console.print(Panel.fit(
        "[bold blue]Advanced AI Agent[/bold blue]\n"
        "[green]A powerful terminal AI coding agent with Gemini API integration[/green]\n"
        "Type [bold yellow]/help[/bold yellow] for a list of commands.",
        title="Welcome",
        border_style="blue"
    ))

def display_help() -> None:
    """Display the help message."""
    console.print(Panel.fit(
        "Available commands:\n"
        "[bold yellow]/help[/bold yellow] - Show this help message\n"
        "[bold yellow]/exit[/bold yellow], [bold yellow]/quit[/bold yellow] - Exit the program\n"
        "[bold yellow]/history[/bold yellow] - Show the conversation history\n"
        "[bold yellow]/list[/bold yellow] - List all conversations\n"
        "[bold yellow]/load[/bold yellow] <id> - Load a conversation\n"
        "[bold yellow]/new[/bold yellow] <name> - Create a new conversation\n"
        "[bold yellow]/delete[/bold yellow] <id> - Delete a conversation\n"
        "[bold yellow]/save[/bold yellow] - Save the current conversation\n"
        "[bold yellow]/export[/bold yellow] <path> - Export the current conversation to a file\n"
        "[bold yellow]/clear[/bold yellow] - Clear the screen\n"
        "[bold yellow]/models[/bold yellow] - List available models\n"
        "[bold yellow]/config[/bold yellow] - Show the current configuration\n"
        "[bold yellow]/debug[/bold yellow] <on|off> - Enable or disable debug mode\n"
        "[bold yellow]/color[/bold yellow] <color> - Set the response text color (blue, green, cyan, etc.)\n"
        "[bold yellow]/logging[/bold yellow] <on|off> - Enable or disable console logging\n"
        "[bold yellow]/iterative[/bold yellow] - Enable step-by-step iterative execution mode\n"
        "[bold yellow]/performance[/bold yellow] - Toggle performance monitoring and optimization\n"
        "[bold yellow]/project[/bold yellow] - Analyze current project structure and health\n"
        "[bold yellow]/test[/bold yellow] <type> - Run comprehensive tests (all, tools, integration, performance)\n"
        "[bold yellow]/health[/bold yellow] - Check system health and component status\n"
        "[bold yellow]/version[/bold yellow] - Show the version information\n\n"
        "[bold yellow]![/bold yellow][command] - Execute a shell command directly (e.g., [bold yellow]!pwd && ls -l[/bold yellow])",
        title="Help",
        border_style="blue"
    ))

def format_response(response: str) -> None:
    """Format and display a response.

    Args:
        response: The response to format and display.
    """
    # Load config to get consistent color
    config = load_config()
    response_color = config.consistent_color

    # Check for code blocks
    lines = response.split("\n")
    in_code_block = False
    code_block_lang = ""
    code_block_content = []

    for i, line in enumerate(lines):
        if line.startswith("```") and not in_code_block:
            # Start of a code block
            in_code_block = True
            code_block_lang = line[3:].strip()
            code_block_content = []
        elif line.startswith("```") and in_code_block:
            # End of a code block
            in_code_block = False

            # Display the code block
            if code_block_content:
                try:
                    console.print(Syntax(
                        "\n".join(code_block_content),
                        code_block_lang or "text",
                        theme="monokai",
                        line_numbers=True
                    ))
                except Exception:
                    # Fallback to plain text if Syntax highlighting fails
                    console.print(f"[{response_color}]" + "\n".join(code_block_content) + f"[/{response_color}]")
        elif in_code_block:
            # Inside a code block
            code_block_content.append(line)
        else:
            # Regular text
            if i > 0 and not lines[i-1].startswith("```"):
                console.print(f"[{response_color}]{line}[/{response_color}]")

    # If we're still in a code block at the end, display it
    if in_code_block and code_block_content:
        try:
            console.print(Syntax(
                "\n".join(code_block_content),
                code_block_lang or "text",
                theme="monokai",
                line_numbers=True
            ))
        except Exception:
            # Fallback to plain text if Syntax highlighting fails
            console.print(f"[{response_color}]" + "\n".join(code_block_content) + f"[/{response_color}]")

def handle_command(command: str, agent: EnhancedAgent) -> bool:
    """Handle a command.

    Args:
        command: The command to handle.
        agent: The agent to use.

    Returns:
        Whether to continue the conversation.
    """
    # Split the command into parts
    parts = command.split(maxsplit=1)
    cmd = parts[0].lower()
    args = parts[1] if len(parts) > 1 else ""

    if cmd in ["/exit", "/quit"]:
        # Exit the program
        return False

    elif cmd == "/help":
        # Show help
        display_help()

    elif cmd == "/history":
        # Show the conversation history
        if agent.conversation_manager.current_conversation:
            conversation = agent.conversation_manager.current_conversation
            console.print(Panel.fit(
                f"[bold blue]Conversation: {conversation.name}[/bold blue]\n"
                f"[green]ID: {conversation.id}[/green]\n"
                f"Created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(conversation.created_at))}\n"
                f"Updated: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(conversation.updated_at))}\n"
                f"Messages: {len(conversation.messages)}",
                title="Conversation Info",
                border_style="blue"
            ))

            # Display the messages
            for message in conversation.messages:
                role_color = {
                    "user": "green",
                    "assistant": "blue",
                    "tool": "yellow",
                    "tool_result": "cyan"
                }.get(message.role, "white")

                console.print(f"[bold {role_color}]{message.role.upper()}:[/bold {role_color}]")
                console.print(message.content)
                console.print()
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/list":
        # List all conversations
        conversations = agent.conversation_manager.list_conversations()

        if conversations:
            console.print(Panel.fit(
                "\n".join([
                    f"[bold blue]{i+1}.[/bold blue] [green]{c['name']}[/green] "
                    f"(ID: {c['id']}, Messages: {c['message_count']}, "
                    f"Updated: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(c['updated_at']))})"
                    for i, c in enumerate(conversations)
                ]),
                title=f"Conversations ({len(conversations)})",
                border_style="blue"
            ))
        else:
            print_warning("No conversations found.")

    elif cmd == "/load":
        # Load a conversation
        if not args:
            print_error("No conversation ID specified.")
            return True

        conversation = agent.conversation_manager.load_conversation(args)
        if conversation:
            print_success(f"Loaded conversation: {conversation.name}")
        else:
            print_error(f"Conversation not found: {args}")

    elif cmd == "/new":
        # Create a new conversation
        name = args or None
        conversation = agent.conversation_manager.new_conversation(name)
        print_success(f"Created new conversation: {conversation.name}")

    elif cmd == "/delete":
        # Delete a conversation
        if not args:
            print_error("No conversation ID specified.")
            return True

        if agent.conversation_manager.delete_conversation(args):
            print_success(f"Deleted conversation: {args}")
        else:
            print_error(f"Conversation not found: {args}")

    elif cmd == "/save":
        # Save the current conversation
        if agent.conversation_manager.current_conversation:
            agent.conversation_manager.save_conversation()
            print_success("Conversation saved.")
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/export":
        # Export the current conversation
        if not args:
            print_error("No export path specified.")
            return True

        if agent.conversation_manager.current_conversation:
            try:
                export_path = Path(args)
                agent.conversation_manager.current_conversation.save(export_path.parent)
                print_success(f"Conversation exported to: {export_path}")
            except Exception as e:
                print_error(f"Error exporting conversation: {e}")
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/clear":
        # Clear the screen
        os.system("cls" if os.name == "nt" else "clear")

    elif cmd == "/models":
        # List available models
        from config import get_available_models

        models = get_available_models()

        if models:
            console.print(Panel.fit(
                "\n".join([
                    f"[bold blue]{provider}:[/bold blue]\n" +
                    "\n".join([f"  [green]{model}[/green]" for model in provider_models])
                    for provider, provider_models in models.items()
                ]),
                title="Available Models",
                border_style="blue"
            ))
        else:
            print_warning("No models available.")

    elif cmd == "/config":
        # Show the current configuration
        config = load_config()

        console.print(Panel.fit(
            f"[bold blue]Agent:[/bold blue]\n"
            f"  Model: {config.agent.model}\n"
            f"  Provider: {config.agent.provider}\n"
            f"  Temperature: {config.agent.temperature}\n"
            f"  Max Tokens: {config.agent.max_tokens}\n\n"
            f"[bold blue]Directories:[/bold blue]\n"
            f"  History: {config.history_dir}\n"
            f"  Logs: {config.log_dir}\n"
            f"  Workspace: {config.workspace_dir or 'Not set'}\n\n"
            f"[bold blue]Display:[/bold blue]\n"
            f"  Response Color: {config.consistent_color}\n"
            f"  Silent Logging: {'Enabled' if config.silent_logging else 'Disabled'}\n\n"
            f"[bold blue]Debug:[/bold blue] {'Enabled' if config.debug else 'Disabled'}",
            title="Configuration",
            border_style="blue"
        ))

    elif cmd == "/debug":
        # Enable or disable debug mode
        if not args:
            print_error("No debug mode specified.")
            return True

        if args.lower() in ["on", "true", "yes", "1"]:
            config = load_config()
            config.debug = True
            save_config(config)
            print_success("Debug mode enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            config = load_config()
            config.debug = False
            save_config(config)
            print_success("Debug mode disabled.")
        else:
            print_error(f"Invalid debug mode: {args}")

    elif cmd == "/version":
        # Show the version information
        import __init__
        __version__ = __init__.__version__

        console.print(Panel.fit(
            f"[bold blue]Advanced AI Agent[/bold blue] v{__version__}\n"
            f"[green]A powerful terminal AI coding agent with Gemini API integration[/green]",
            title="Version",
            border_style="blue"
        ))

    elif cmd == "/color":
        # Set the response text color
        if not args:
            print_error("No color specified.")
            return True

        # Valid colors
        valid_colors = ["black", "red", "green", "yellow", "blue", "magenta", "cyan", "white"]

        if args.lower() not in valid_colors:
            print_error(f"Invalid color: {args}. Valid colors are: {', '.join(valid_colors)}")
            return True

        # Update the configuration
        config = load_config()
        config.consistent_color = args.lower()
        save_config(config)
        print_success(f"Response text color set to: {args.lower()}")

    elif cmd == "/logging":
        # Enable or disable console logging
        if not args:
            print_error("No logging mode specified.")
            return True

        if args.lower() in ["on", "true", "yes", "1"]:
            config = load_config()
            config.silent_logging = False
            save_config(config)
            # Immediately update the logger
            setup_logger(config.log_dir, config.debug, silent=False)
            print_success("Console logging enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            config = load_config()
            config.silent_logging = True
            save_config(config)
            # Immediately update the logger
            setup_logger(config.log_dir, config.debug, silent=True)
            print_success("Console logging disabled.")
        else:
            print_error(f"Invalid logging mode: {args}")

    elif cmd == "/iterative":
        # Toggle iterative execution mode
        if not args:
            # Toggle mode
            if hasattr(agent, 'iterative_mode') and agent.iterative_mode:
                agent.iterative_mode = False
                console.print(Panel.fit(
                    "[bold yellow]Iterative Execution Mode Disabled[/bold yellow]\n"
                    "The agent will now use standard execution mode.",
                    title="Standard Mode",
                    border_style="yellow"
                ))
                print_success("Iterative execution mode disabled. Switched to standard processing.")
            else:
                agent.iterative_mode = True
                console.print(Panel.fit(
                    "[bold green]Iterative Execution Mode Enabled[/bold green]\n"
                    "The agent will now execute requests step-by-step with detailed analysis.\n"
                    "Each step will be analyzed, planned, and validated before proceeding.\n"
                    "This provides maximum transparency and control over the execution process.",
                    title="Iterative Mode",
                    border_style="green"
                ))
                print_success("Iterative execution mode enabled. Your next requests will be processed step-by-step.")
        elif args.lower() in ["on", "true", "yes", "1"]:
            agent.iterative_mode = True
            print_success("Iterative execution mode enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            agent.iterative_mode = False
            print_success("Iterative execution mode disabled.")
        else:
            print_error(f"Invalid iterative mode: {args}. Use 'on' or 'off', or no argument to toggle.")

    elif cmd == "/performance":
        # Toggle performance monitoring mode
        if not hasattr(agent, 'performance_mode'):
            agent.performance_mode = False

        if not args:
            # Toggle mode
            agent.performance_mode = not agent.performance_mode
            status = "enabled" if agent.performance_mode else "disabled"
            console.print(Panel.fit(
                f"[bold {'green' if agent.performance_mode else 'yellow'}]Performance Monitoring {status.title()}[/bold {'green' if agent.performance_mode else 'yellow'}]\n"
                f"Performance optimization and monitoring is now {status}.",
                title="Performance Mode",
                border_style="green" if agent.performance_mode else "yellow"
            ))
            print_success(f"Performance monitoring {status}.")
        elif args.lower() in ["on", "true", "yes", "1"]:
            agent.performance_mode = True
            print_success("Performance monitoring enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            agent.performance_mode = False
            print_success("Performance monitoring disabled.")
        else:
            print_error(f"Invalid performance mode: {args}. Use 'on' or 'off', or no argument to toggle.")

    elif cmd == "/project":
        # Analyze project structure
        console.print("[bold green]Analyzing project structure...[/bold green]")

        try:
            project_path = args if args else None
            analysis = agent.analyze_project_structure(project_path)

            # Display analysis results
            console.print(Panel.fit(
                f"[bold blue]Project Analysis Results[/bold blue]\n\n"
                f"[bold]Project Type:[/bold] {analysis.get('project_type', 'Unknown')}\n"
                f"[bold]Languages:[/bold] {', '.join(analysis.get('languages', ['None detected']))}\n"
                f"[bold]Frameworks:[/bold] {', '.join(analysis.get('frameworks', ['None detected']))}\n"
                f"[bold]Build Tools:[/bold] {', '.join(analysis.get('build_tools', ['None detected']))}\n"
                f"[bold]Test Frameworks:[/bold] {', '.join(analysis.get('test_frameworks', ['None detected']))}\n"
                f"[bold]Deployment:[/bold] {', '.join(analysis.get('deployment_configs', ['None detected']))}\n\n"
                f"[bold]Total Files:[/bold] {analysis.get('structure', {}).get('total_files', 0)}\n"
                f"[bold]Source Files:[/bold] {len(analysis.get('structure', {}).get('source_files', []))}\n"
                f"[bold]Test Files:[/bold] {len(analysis.get('structure', {}).get('test_files', []))}\n"
                f"[bold]Config Files:[/bold] {len(analysis.get('structure', {}).get('config_files', []))}",
                title="Project Overview",
                border_style="blue"
            ))

            # Display issues if any
            issues = analysis.get('issues', [])
            if issues:
                console.print(Panel.fit(
                    "\n".join([f"• {issue}" for issue in issues]),
                    title="[bold red]Issues Found[/bold red]",
                    border_style="red"
                ))

            # Display recommendations
            recommendations = analysis.get('recommendations', [])
            if recommendations:
                console.print(Panel.fit(
                    "\n".join([f"• {rec}" for rec in recommendations]),
                    title="[bold yellow]Recommendations[/bold yellow]",
                    border_style="yellow"
                ))

            # Display dependencies summary
            deps = analysis.get('dependencies', {})
            if deps.get('dependencies'):
                dep_count = len(deps['dependencies'])
                dev_dep_count = len(deps.get('dev_dependencies', {}))
                console.print(Panel.fit(
                    f"[bold]Package Managers:[/bold] {', '.join(deps.get('package_managers', ['None']))}\n"
                    f"[bold]Dependencies:[/bold] {dep_count}\n"
                    f"[bold]Dev Dependencies:[/bold] {dev_dep_count}",
                    title="Dependencies",
                    border_style="green"
                ))

            print_success("Project analysis completed successfully.")

        except Exception as e:
            print_error(f"Project analysis failed: {e}")

    elif cmd == "/test":
        # Run comprehensive tests
        test_type = args if args else "all"
        valid_types = ["all", "tools", "integration", "performance"]

        if test_type not in valid_types:
            print_error(f"Invalid test type: {test_type}. Valid types: {', '.join(valid_types)}")
            return True

        console.print(f"[bold green]Running {test_type} tests...[/bold green]")

        try:
            test_results = agent.run_comprehensive_tests(test_type)

            # Display test summary
            total = test_results.get('total_tests', 0)
            passed = test_results.get('passed_tests', 0)
            failed = test_results.get('failed_tests', 0)
            success_rate = (passed / total * 100) if total > 0 else 0

            status_color = "green" if success_rate >= 80 else "yellow" if success_rate >= 60 else "red"

            console.print(Panel.fit(
                f"[bold]Test Summary[/bold]\n\n"
                f"[bold]Total Tests:[/bold] {total}\n"
                f"[bold]Passed:[/bold] [green]{passed}[/green]\n"
                f"[bold]Failed:[/bold] [red]{failed}[/red]\n"
                f"[bold]Success Rate:[/bold] [{status_color}]{success_rate:.1f}%[/{status_color}]",
                title="Test Results",
                border_style=status_color
            ))

            # Display detailed results for failed tests
            test_details = test_results.get('test_details', {})
            for category, details in test_details.items():
                if details.get('failed', 0) > 0:
                    console.print(f"\n[bold red]Failed {category.title()} Tests:[/bold red]")
                    for test_name, test_result in details.get('details', {}).items():
                        if test_result.get('status') == 'failed':
                            error = test_result.get('error', 'Unknown error')
                            console.print(f"  • {test_name}: {error}")

            # Display recommendations
            recommendations = test_results.get('recommendations', [])
            if recommendations:
                console.print(Panel.fit(
                    "\n".join([f"• {rec}" for rec in recommendations]),
                    title="[bold yellow]Recommendations[/bold yellow]",
                    border_style="yellow"
                ))

            # Display performance metrics if available
            perf_metrics = test_results.get('performance_metrics', {})
            if perf_metrics:
                console.print(Panel.fit(
                    f"[bold]Response Time:[/bold] {perf_metrics.get('response_time', 'N/A'):.2f}s\n"
                    f"[bold]Memory Usage:[/bold] {perf_metrics.get('memory_usage', 'N/A'):.1f}MB",
                    title="Performance Metrics",
                    border_style="blue"
                ))

            print_success(f"Test execution completed. {passed}/{total} tests passed.")

        except Exception as e:
            print_error(f"Test execution failed: {e}")

    elif cmd == "/health":
        # Check system health
        console.print("[bold green]Checking system health...[/bold green]")

        try:
            health_check = agent.validate_system_health()

            # Display overall status
            status = health_check.get('overall_status', 'unknown')
            status_color = {
                'healthy': 'green',
                'degraded': 'yellow',
                'unhealthy': 'red',
                'error': 'red'
            }.get(status, 'white')

            console.print(Panel.fit(
                f"[bold]Overall Status:[/bold] [{status_color}]{status.upper()}[/{status_color}]\n"
                f"[bold]Timestamp:[/bold] {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(health_check.get('timestamp', time.time())))}",
                title="System Health",
                border_style=status_color
            ))

            # Display component status
            components = health_check.get('components', {})
            if components:
                component_status = []
                for component, status in components.items():
                    color = {'healthy': 'green', 'partial': 'yellow', 'missing': 'yellow', 'error': 'red'}.get(status, 'white')
                    component_status.append(f"[bold]{component}:[/bold] [{color}]{status}[/{color}]")

                console.print(Panel.fit(
                    "\n".join(component_status),
                    title="Component Status",
                    border_style="blue"
                ))

            # Display warnings
            warnings = health_check.get('warnings', [])
            if warnings:
                console.print(Panel.fit(
                    "\n".join([f"• {warning}" for warning in warnings]),
                    title="[bold yellow]Warnings[/bold yellow]",
                    border_style="yellow"
                ))

            # Display errors
            errors = health_check.get('errors', [])
            if errors:
                console.print(Panel.fit(
                    "\n".join([f"• {error}" for error in errors]),
                    title="[bold red]Errors[/bold red]",
                    border_style="red"
                ))

            # Display recommendations
            recommendations = health_check.get('recommendations', [])
            if recommendations:
                console.print(Panel.fit(
                    "\n".join([f"• {rec}" for rec in recommendations]),
                    title="[bold cyan]Recommendations[/bold cyan]",
                    border_style="cyan"
                ))

            print_success("Health check completed.")

        except Exception as e:
            print_error(f"Health check failed: {e}")

    else:
        # Unknown command
        print_error(f"Unknown command: {cmd}")

    return True

@click.command()
@click.option("--model", "-m", help="The model to use.")
@click.option("--provider", "-p", help="The provider to use.")
@click.option("--workspace", "-w", help="The workspace directory to use.")
@click.option("--debug", "-d", is_flag=True, help="Enable debug mode.")
@click.option("--version", "-v", is_flag=True, help="Show the version information.")
@click.argument("message", required=False)
def cli(model: Optional[str], provider: Optional[str], workspace: Optional[str], debug: bool, version: bool, message: Optional[str]) -> None:
    """Advanced AI Agent - A powerful terminal AI coding agent with Gemini API integration."""
    # Show the version information if requested
    if version:
        import __init__
        __version__ = __init__.__version__

        console.print(Panel.fit(
            f"[bold blue]Advanced AI Agent[/bold blue] v{__version__}\n"
            f"[green]A powerful terminal AI coding agent with Gemini API integration[/green]",
            title="Version",
            border_style="blue"
        ))
        return

    # Load the configuration
    config = load_config()

    # Update the configuration with command-line options
    if model:
        config.agent.model = model
    if provider:
        config.agent.provider = provider
    if workspace:
        config.workspace_dir = Path(workspace)
    if debug:
        config.debug = True

    # Save the updated configuration
    save_config(config)

    # Set up the logger with silent mode from config
    logger = setup_logger(config.log_dir, config.debug, silent=config.silent_logging)

    # Create the model manager
    model_manager = ModelManager(
        provider=config.agent.provider,
        model_name=config.agent.model,
        temperature=config.agent.temperature,
        max_tokens=config.agent.max_tokens
    )

    # Create the conversation manager
    conversation_manager = ConversationManager(config.history_dir)

    # Create the enhanced agent
    agent = EnhancedAgent(
        model_manager=model_manager,
        conversation_manager=conversation_manager,
        workspace_dir=config.workspace_dir
    )

    # Create a new conversation
    conversation_manager.new_conversation()

    # If a message was provided, process it and exit
    if message:
        response = agent.process_message(message)
        format_response(response)
        return

    # Display the welcome message
    display_welcome_message()

    # Create the prompt session
    history_file = get_config_dir() / "history.txt"
    session = PromptSession(
        history=FileHistory(str(history_file)),
        auto_suggest=AutoSuggestFromHistory(),
        completer=command_completer,
        style=style
    )

    # Main loop
    while True:
        try:
            # Get the user input with mode indicator
            mode_indicator = " [ITERATIVE]" if hasattr(agent, 'iterative_mode') and agent.iterative_mode else ""
            user_input = session.prompt(
                [("class:prompt", f">>>{mode_indicator} ")],
                style=style
            )

            # Skip empty input
            if not user_input.strip():
                continue

            # Handle commands
            if user_input.startswith("/"):
                if not handle_command(user_input, agent):
                    break
                continue

            # Handle direct shell commands
            if user_input.startswith("!"):
                # Extract the shell command (remove the ! prefix)
                shell_command = user_input[1:].strip()
                if shell_command:
                    try:
                        # First, try using the platform's native shell directly
                        is_windows = os.name == 'nt'

                        if is_windows:
                            # On Windows, try cmd.exe first for complex commands
                            if any(char in shell_command for char in "|&><;"):
                                try:
                                    process = subprocess.run(
                                        ["cmd.exe", "/c", shell_command],
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        universal_newlines=True,
                                        cwd=os.getcwd()
                                    )
                                    stdout = process.stdout
                                    stderr = process.stderr
                                    return_code = process.returncode
                                except Exception as e:
                                    # Fall back to the shell tool
                                    logger.warning(f"Failed to execute with cmd.exe: {e}")
                                    stdout, stderr, return_code = agent.shell_tool.execute(shell_command)
                            else:
                                # For simple commands, use the shell tool
                                stdout, stderr, return_code = agent.shell_tool.execute(shell_command)
                        else:
                            # On Unix-like systems, try bash directly
                            try:
                                process = subprocess.run(
                                    ["bash", "-c", shell_command],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    universal_newlines=True,
                                    cwd=os.getcwd()
                                )
                                stdout = process.stdout
                                stderr = process.stderr
                                return_code = process.returncode
                            except Exception as e:
                                # Fall back to the shell tool
                                logger.warning(f"Failed to execute with bash: {e}")
                                stdout, stderr, return_code = agent.shell_tool.execute(shell_command)

                        # Display the results
                        if return_code == 0:
                            if stdout.strip():
                                console.print(f"[bold green]Command executed successfully:[/bold green]")
                                console.print(stdout)
                            else:
                                console.print("[bold green]Command executed successfully.[/bold green]")
                        else:
                            console.print(f"[bold red]Command failed with return code {return_code}:[/bold red]")
                            if stderr.strip():
                                console.print(stderr)
                    except Exception as e:
                        # If all else fails, try one more approach with the system shell
                        try:
                            # Use the system shell directly as a last resort
                            process = subprocess.run(
                                shell_command,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                shell=True,
                                universal_newlines=True,
                                cwd=os.getcwd()
                            )
                            stdout = process.stdout
                            stderr = process.stderr
                            return_code = process.returncode

                            # Display the results
                            if return_code == 0:
                                if stdout.strip():
                                    console.print(f"[bold green]Command executed successfully:[/bold green]")
                                    console.print(stdout)
                                else:
                                    console.print("[bold green]Command executed successfully.[/bold green]")
                            else:
                                console.print(f"[bold red]Command failed with return code {return_code}:[/bold red]")
                                if stderr.strip():
                                    console.print(stderr)
                        except Exception as final_e:
                            console.print(f"[bold red]Error executing command: {final_e}[/bold red]")
                else:
                    print_error("No command specified after '!'")
                continue

            # Process the user input
            console.print("[bold green]Thinking...[/bold green]")

            # Get consistent color from config
            response_color = config.consistent_color

            # Check if iterative mode is enabled
            if hasattr(agent, 'iterative_mode') and agent.iterative_mode:
                # Use iterative execution with error recovery
                console.print(f"[bold {response_color}]Assistant (Iterative Mode):[/bold {response_color}]")
                try:
                    for status_update in agent.execute_iteratively(user_input):
                        console.print(f"[{response_color}]{status_update}[/{response_color}]")
                        time.sleep(0.1)  # Small delay for readability
                except Exception as e:
                    console.print(f"[bold red]Error in iterative execution: {e}[/bold red]")
                    # Try error recovery execution
                    console.print(f"[bold yellow]Attempting error recovery...[/bold yellow]")
                    try:
                        recovery_response = agent.execute_with_error_recovery(user_input)
                        console.print(f"[{response_color}]{recovery_response}[/{response_color}]")
                    except Exception as recovery_error:
                        console.print(f"[bold red]Recovery also failed: {recovery_error}[/bold red]")
                        # Final fallback to streaming with typewriter effect
                        console.print(f"[bold yellow]🔄 Using streaming fallback...[/bold yellow]")
                        try:
                            for chunk in agent.stream_process_message(user_input):
                                console.print(f"[{response_color}]{chunk}[/{response_color}]", end="")
                                sys.stdout.flush()  # Real-time display
                        except Exception as final_error:
                            console.print(f"[bold red]❌ All processing methods failed: {final_error}[/bold red]")
            else:
                # Use modern AI agent streaming with typewriter effect
                console.print(f"[bold {response_color}]🤖 Assistant:[/bold {response_color}]")
                try:
                    # Use streaming for typewriter effect like modern AI agents
                    for chunk in agent.stream_process_message(user_input):
                        # Print each character with proper formatting
                        console.print(f"[{response_color}]{chunk}[/{response_color}]", end="")
                        # Flush output for real-time display
                        sys.stdout.flush()
                except Exception as e:
                    console.print(f"\n[bold red]❌ Error in streaming: {e}[/bold red]")
                    # Fallback to regular processing
                    console.print(f"[bold yellow]🔄 Trying fallback processing...[/bold yellow]")
                    try:
                        response = agent.process_message(user_input)
                        console.print(f"[{response_color}]{response}[/{response_color}]")
                    except Exception as fallback_error:
                        console.print(f"[bold red]❌ All processing methods failed: {fallback_error}[/bold red]")
            console.print()

        except KeyboardInterrupt:
            # Exit on Ctrl+C
            break

        except Exception as e:
            print_error(f"Error: {e}")
            if config.debug:
                import traceback
                traceback.print_exc()

    print_info("Goodbye!")

if __name__ == "__main__":
    cli()
